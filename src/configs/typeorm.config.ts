import {
  BusinessFunctionEntity,
  DeliverableEntity,
  DeliverableOwnerEntity,
  DeliverableTypeEntity,
  EmployeeEntity,
  EmployeeProfilesEntity,
  ProposalEntity,
  RoleEntity,
  TargetEntity,
  TargetTypeEntity,
  ProposalCommentsEntity,
  ZoneEntity,
  TargetCommentsEntity,
} from '@ghq-abi/northstar-domain';
import { ProposalEmployeeEntity } from '@ghq-abi/northstar-domain/dist/entities/proposals-employees.entity';
import { registerAs } from '@nestjs/config';
import { config as dotenvConfig } from 'dotenv';
import { DataSourceOptions } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

dotenvConfig({ path: '.env' });

const config: DataSourceOptions = {
  type: 'mssql',
  host: `${process.env.DATABASE_HOST}`,
  port: +process.env.DATABASE_PORT,
  username: `${process.env.DATABASE_USERNAME}`,
  password: `${process.env.DATABASE_PASSWORD}`,
  database: `${process.env.DATABASE_NAME}`,
  schema: `${process.env.DATABASE_SCHEMA}`,
  synchronize: false,
  options: { encrypt: true },
  namingStrategy: new SnakeNamingStrategy(),
  requestTimeout: 30000,
  connectionTimeout: 30000,
  entities: [
    DeliverableEntity,
    DeliverableOwnerEntity,
    DeliverableTypeEntity,
    ProposalEntity,
    TargetEntity,
    TargetTypeEntity,
    ProposalEmployeeEntity,
    EmployeeEntity,
    BusinessFunctionEntity,
    ProposalCommentsEntity,
    EmployeeProfilesEntity,
    RoleEntity,
    ZoneEntity,
    TargetCommentsEntity,
  ],
};

export default registerAs('typeorm', () => config);
