import { Modu<PERSON> } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { EmployeeController } from './employee.controller';
import {
  ProposalEmployeeEntity,
  ProposalEmployeeRepository,
} from '@ghq-abi/northstar-domain';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TargetTypeModule } from '../target-type/target-type.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProposalEmployeeEntity]),
    TargetTypeModule,
  ],
  controllers: [EmployeeController],
  providers: [EmployeeService, ProposalEmployeeRepository],
  exports: [
    TypeOrmModule.forFeature([ProposalEmployeeEntity]),
    EmployeeService,
    ProposalEmployeeRepository,
  ],
})
export class EmployeeModule {}
