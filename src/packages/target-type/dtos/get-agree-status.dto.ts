import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class TargetTypeIdentifier {
  @ApiProperty({
    description: 'Unique identifier for the target',
    example: 'A70BB61B-47DF-4A4B-9525-36118C76AD8A',
  })
  @IsString()
  @IsNotEmpty()
  uidTarget: string;

  @ApiProperty({
    description: 'Unique identifier for the target type',
    example: 'B80CC62C-58EE-5B5C-C636-47229D87BE9B',
  })
  @IsString()
  @IsNotEmpty()
  uid: string;
}

export class GetAgreeStatusDto {
  @ApiProperty({
    description: 'Array of target type identifiers to get agreement status',
    example: [
      {
        uidTarget: 'A70BB61B-47DF-4A4B-9525-36118C76AD8A',
        uid: 'B80CC62C-58EE-5B5C-C636-47229D87BE9B',
      },
      {
        uidTarget: 'C90DD73D-69FF-6C6D-D747-58330E98CF0C',
        uid: 'D00EE84E-7A00-7D7E-E858-69441F09D01D',
      },
    ],
    type: [TargetTypeIdentifier],
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => TargetTypeIdentifier)
  targetTypeIdentifiers: TargetTypeIdentifier[];
}
